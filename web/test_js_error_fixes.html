<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Error Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 3px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 3px;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 3px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>JavaScript Error Fixes Test</h1>
    <p>This page tests the JavaScript null reference fixes implemented for the NewsMonitor application.</p>
    
    <div class="test-section">
        <h3>Test 1: DOM Element Null Checks</h3>
        <p>Testing getElementById calls with null checks for elements that don't exist.</p>
        <button onclick="testDOMNullChecks()">Run Test</button>
        <div id="test1-results"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Safe Property Setting</h3>
        <p>Testing safe property setting on DOM elements.</p>
        <button onclick="testSafePropertySetting()">Run Test</button>
        <div id="test2-results"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Event Listener Safety</h3>
        <p>Testing safe event listener attachment.</p>
        <button onclick="testEventListenerSafety()">Run Test</button>
        <div id="test3-results"></div>
    </div>
    
    <div id="test-results">
        <h3>Overall Test Results</h3>
        <div id="overall-status" class="info">Click the test buttons above to run individual tests.</div>
    </div>

    <script>
        // Global test results
        let testResults = {
            test1: false,
            test2: false,
            test3: false
        };

        // Test 1: DOM Element Null Checks
        function testDOMNullChecks() {
            const resultsDiv = document.getElementById('test1-results');
            let passed = true;
            let messages = [];

            try {
                // Test getElementById with null checks (simulating our graph.js fixes)
                const nonExistentElement = document.getElementById('non-existent-element');
                if (nonExistentElement) {
                    nonExistentElement.textContent = 'This should not execute';
                    messages.push('✗ Null check failed - should not reach this code');
                    passed = false;
                } else {
                    messages.push('✓ Null check for non-existent element works correctly');
                }

                // Test multiple element access
                const elements = ['missing-1', 'missing-2', 'missing-3'];
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.classList.add('active');
                        messages.push(`✗ Element ${id} should not exist`);
                        passed = false;
                    } else {
                        messages.push(`✓ Null check for ${id} works correctly`);
                    }
                });

                testResults.test1 = passed;
                resultsDiv.innerHTML = `<div class="${passed ? 'success' : 'error'}">Test 1 ${passed ? 'PASSED' : 'FAILED'}<br>${messages.join('<br>')}</div>`;
            } catch (error) {
                testResults.test1 = false;
                resultsDiv.innerHTML = `<div class="error">Test 1 FAILED: ${error.message}</div>`;
            }

            updateOverallStatus();
        }

        // Test 2: Safe Property Setting
        function testSafePropertySetting() {
            const resultsDiv = document.getElementById('test2-results');
            let passed = true;
            let messages = [];

            try {
                // Create test elements
                const testContainer = document.createElement('div');
                testContainer.innerHTML = `
                    <div id="test-price">$0.00</div>
                    <div id="test-change">+0.00 (0.00%)</div>
                    <div id="test-volume">0</div>
                `;
                resultsDiv.appendChild(testContainer);

                // Test safe DOM updates (simulating our price update fixes)
                const priceElement = document.getElementById('test-price');
                if (priceElement) {
                    priceElement.textContent = '$4,567.89';
                    messages.push('✓ Price element updated safely');
                } else {
                    messages.push('✗ Price element not found');
                    passed = false;
                }

                const changeElement = document.getElementById('test-change');
                if (changeElement) {
                    changeElement.textContent = '+12.34 (+0.27%)';
                    changeElement.className = 'price-up';
                    messages.push('✓ Change element updated safely');
                } else {
                    messages.push('✗ Change element not found');
                    passed = false;
                }

                const volumeElement = document.getElementById('test-volume');
                if (volumeElement) {
                    volumeElement.textContent = '1,234,567';
                    messages.push('✓ Volume element updated safely');
                } else {
                    messages.push('✗ Volume element not found');
                    passed = false;
                }

                // Test with missing element
                const missingElement = document.getElementById('test-missing');
                if (missingElement) {
                    missingElement.textContent = 'This should not execute';
                    messages.push('✗ Missing element check failed');
                    passed = false;
                } else {
                    messages.push('✓ Missing element handled safely');
                }

                testResults.test2 = passed;
                resultsDiv.innerHTML = `<div class="${passed ? 'success' : 'error'}">Test 2 ${passed ? 'PASSED' : 'FAILED'}<br>${messages.join('<br>')}</div>` + testContainer.outerHTML;
            } catch (error) {
                testResults.test2 = false;
                resultsDiv.innerHTML = `<div class="error">Test 2 FAILED: ${error.message}</div>`;
            }

            updateOverallStatus();
        }

        // Test 3: Event Listener Safety
        function testEventListenerSafety() {
            const resultsDiv = document.getElementById('test3-results');
            let passed = true;
            let messages = [];

            try {
                // Test safe event listener attachment
                const testButton = document.createElement('button');
                testButton.id = 'test-button';
                testButton.textContent = 'Test Button';
                resultsDiv.appendChild(testButton);

                // Safe event listener attachment
                const button = document.getElementById('test-button');
                if (button) {
                    button.addEventListener('click', function() {
                        this.textContent = 'Clicked!';
                    });
                    messages.push('✓ Event listener attached safely');
                    
                    // Trigger the event
                    button.click();
                    if (button.textContent === 'Clicked!') {
                        messages.push('✓ Event listener executed successfully');
                    } else {
                        messages.push('✗ Event listener did not execute');
                        passed = false;
                    }
                } else {
                    messages.push('✗ Test button not found');
                    passed = false;
                }

                // Test with non-existent element
                const nonExistentButton = document.getElementById('non-existent-button');
                if (nonExistentButton) {
                    nonExistentButton.addEventListener('click', function() {});
                    messages.push('✗ Should not attach listener to non-existent element');
                    passed = false;
                } else {
                    messages.push('✓ Non-existent element handled safely');
                }

                testResults.test3 = passed;
                resultsDiv.innerHTML = `<div class="${passed ? 'success' : 'error'}">Test 3 ${passed ? 'PASSED' : 'FAILED'}<br>${messages.join('<br>')}</div>`;
            } catch (error) {
                testResults.test3 = false;
                resultsDiv.innerHTML = `<div class="error">Test 3 FAILED: ${error.message}</div>`;
            }

            updateOverallStatus();
        }

        // Update overall status
        function updateOverallStatus() {
            const statusDiv = document.getElementById('overall-status');
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result === true).length;
            const runTests = Object.values(testResults).filter(result => result !== null).length;

            if (runTests === 0) {
                statusDiv.innerHTML = '<div class="info">Click the test buttons above to run individual tests.</div>';
            } else if (passedTests === totalTests) {
                statusDiv.innerHTML = '<div class="success">🎉 All tests passed! JavaScript error fixes are working correctly.</div>';
            } else {
                statusDiv.innerHTML = `<div class="error">⚠️ ${passedTests}/${runTests} tests passed. Some issues may remain.</div>`;
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('JavaScript Error Fixes Test page loaded successfully');
        });
    </script>
</body>
</html>
