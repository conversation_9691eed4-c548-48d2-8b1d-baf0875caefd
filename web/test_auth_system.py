#!/usr/bin/env python3
"""
Test script for the NewsMonitor authentication system.

This script tests the authentication functionality including:
- User registration
- User login
- Password validation
- Database operations
"""

import traceback
from utils.logging_config import get_web_logger
from db.database import get_db_manager
from db.models import User
from web.auth.utils import create_user, get_user_by_username_or_email, validate_password_strength
from flask import Flask
import os
import sys
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


logger = get_web_logger(__name__)


def create_test_app():
    """Create a test Flask app for authentication testing."""
    app = Flask(__name__)

    # Configure the app
    app.config['SECRET_KEY'] = 'test-secret-key'
    app.config['TESTING'] = True
    app.config['SECURITY_PASSWORD_SALT'] = 'test-salt'
    app.config['BASE_URL'] = 'http://localhost:5000'
    app.config['SERVER_NAME'] = 'localhost:5000'
    app.config['PREFERRED_URL_SCHEME'] = 'http'
    app.config['APPLICATION_ROOT'] = '/web'

    return app


def test_database_connection():
    """Test database connection."""
    print("Testing database connection...")

    try:
        db = get_db_manager()
        with db.connection.get_session() as session:
            # Test basic query
            user_count = session.query(User).count()
            print(
                f"✅ Database connection successful. Found {user_count} users.")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


def test_password_validation():
    """Test password strength validation."""
    print("Testing password validation...")

    test_passwords = [
        ("weak", ["password"]),
        ("medium", ["Password123"]),
        ("strong", ["StrongP@ssw0rd!"]),
        ("very_weak", ["123"])
    ]

    try:
        for strength, passwords in test_passwords:
            for password in passwords:
                errors = validate_password_strength(password)
                print(f"   Password '{password}': {len(errors)} errors")
                if errors:
                    print(f"     Errors: {', '.join(errors)}")

        print("✅ Password validation working correctly")
        return True

    except Exception as e:
        print(f"❌ Password validation failed: {e}")
        return False


def test_user_creation():
    """Test user creation functionality."""
    print("Testing user creation...")

    app = create_test_app()

    with app.app_context():
        try:
            # Clean up any existing test user
            db = get_db_manager()
            with db.connection.get_session() as session:
                existing_user = session.query(User).filter_by(
                    email='<EMAIL>').first()
                if existing_user:
                    session.delete(existing_user)
                    session.commit()
                    print("   Cleaned up existing test user")

            with db.connection.get_session() as session:
                # Create new test user
                user = create_user(
                    username='testauth',
                    email='<EMAIL>',
                    password='TestPassword123!',
                    first_name='Test',
                    last_name='Auth'
                )
                # Rebind the object to the session
                user = session.merge(user)

                if user:
                    print(f"✅ User created successfully with ID: {user.id}")
                    print(f"   Username: {user.username}")
                    print(f"   Email: {user.email}")
                    print(f"   Name: {user.first_name} {user.last_name}")
                    return user
                else:
                    print("❌ User creation failed")
                    return None

        except Exception as e:
            print(f"❌ User creation failed: {e}")
            return None


def test_user_retrieval():
    """Test user retrieval by username and email."""
    print("Testing user retrieval...")

    app = create_test_app()

    with app.app_context():
        try:
            db = get_db_manager()
            with db.connection.get_session() as session:
                # Test retrieval by username
                user_by_username = get_user_by_username_or_email('testauth')
                user_by_username = session.merge(user_by_username)
                if user_by_username:
                    print(
                        f"✅ User found by username: {user_by_username.username}")
                else:
                    print("❌ User not found by username")
                    return False
                # Test retrieval by email
                user_by_email = get_user_by_username_or_email(
                    '<EMAIL>')

                # Rebind the object to the session

                user_by_email = session.merge(user_by_email)
                if user_by_email:
                    print(f"✅ User found by email: {user_by_email.email}")
                else:
                    print("❌ User not found by email")
                    return False

                # Verify they're the same user
                if user_by_username.id == user_by_email.id:
                    print("✅ Username and email retrieval return same user")
                    return True
                else:
                    print("❌ Username and email retrieval return different users")
                    return False

        except Exception as e:
            print(f"❌ User retrieval failed: {e}")
            print(traceback.format_exc())
            return False


def test_password_verification():
    """Test password verification."""
    print("Testing password verification...")

    try:
        db = get_db_manager()
        with db.connection.get_session() as session:
            # Get the test user
            user = get_user_by_username_or_email('<EMAIL>')
            if not user:
                print("❌ Test user not found for password verification")
                return False
            # Rebind the object to the session
            user = session.merge(user)

            # Test correct password
            if user.check_password('TestPassword123!'):
                print("✅ Correct password verified successfully")
            else:
                print("❌ Correct password verification failed")
                return False

            # Test incorrect password
            if not user.check_password('WrongPassword'):
                print("✅ Incorrect password correctly rejected")
            else:
                print("❌ Incorrect password was accepted")
                return False

            return True

    except Exception as e:
        print(f"❌ Password verification failed: {e}")
        return False


def test_user_preferences():
    """Test user preferences functionality."""
    print("Testing user preferences...")

    try:
        db = get_db_manager()
        with db.connection.get_session() as session:
            # Get the test user
            user = get_user_by_username_or_email('<EMAIL>')
            if not user:
                print("❌ Test user not found for preferences test")
                return False
            # Rebind the object to the session
            user = session.merge(user)

            # Check default preferences
            if user.email_preferences:
                print(f"✅ Email preferences found: {user.email_preferences}")
            else:
                print("⚠️  No email preferences set (this is okay)")

            if user.user_preferences:
                print(f"✅ User preferences found: {user.user_preferences}")
            else:
                print("⚠️  No user preferences set (this is okay)")

            return True

    except Exception as e:
        print(f"❌ User preferences test failed: {e}")
        return False


def test_authentication_forms():
    """Test authentication forms."""
    print("Testing authentication forms...")

    app = create_test_app()

    with app.app_context():
        with app.test_request_context(method='POST', data={}):
            try:
                from web.auth.forms import LoginForm, RegistrationForm

                # Test login form
                login_form = LoginForm()
                print("✅ Login form created successfully")

                # Test registration form
                registration_form = RegistrationForm()
                print("✅ Registration form created successfully")

                return True

            except Exception as e:
                print(f"❌ Authentication forms test failed: {e}")
                return False


def run_all_tests():
    """Run all authentication system tests."""
    print("🧪 Starting NewsMonitor Authentication System Tests\n")

    tests = [
        ("Database Connection", test_database_connection),
        ("Password Validation", test_password_validation),
        ("Authentication Forms", test_authentication_forms),
        ("User Creation", test_user_creation),
        ("User Retrieval", test_user_retrieval),
        ("Password Verification", test_password_verification),
        ("User Preferences", test_user_preferences),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Print summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Authentication system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
