#!/usr/bin/env python3
"""
Test script for the enhanced web interface with LLM predictions.

This script tests the LLM prediction functionality without requiring
a full database setup.
"""

import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any

# Mock data for testing
def create_mock_articles() -> List[Dict[str, Any]]:
    """Create mock news articles for testing."""
    return [
        {
            'title': 'Fed Signals Potential Rate Cut as Inflation Cools',
            'content': 'The Federal Reserve indicated today that it may consider cutting interest rates in the coming months as inflation continues to moderate.',
            'source': 'Reuters',
            'publish_time': '2024-01-15T14:30:00Z',
            'sentiment_score': 0.3,
            'url': 'https://example.com/fed-rate-cut'
        },
        {
            'title': 'Tech Stocks Rally on Strong Earnings Reports',
            'content': 'Major technology companies reported better-than-expected quarterly earnings, driving a broad rally in tech stocks.',
            'source': 'CNBC',
            'publish_time': '2024-01-15T16:45:00Z',
            'sentiment_score': 0.6,
            'url': 'https://example.com/tech-rally'
        },
        {
            'title': 'Geopolitical Tensions Rise as Trade Disputes Escalate',
            'content': 'International trade tensions have escalated following new tariff announcements, raising concerns about global economic growth.',
            'source': 'Bloomberg',
            'publish_time': '2024-01-15T12:15:00Z',
            'sentiment_score': -0.4,
            'url': 'https://example.com/trade-tensions'
        }
    ]

def create_mock_llm_prediction() -> Dict[str, Any]:
    """Create mock LLM prediction data for testing."""
    return {
        "prediction": "up",
        "confidence": 0.75,
        "probabilities": {
            "positive": 0.75,
            "negative": 0.15,
            "neutral": 0.10
        },
        "key_evidence": [
            "Federal Reserve signals potential rate cuts, providing monetary stimulus",
            "Strong corporate earnings reports from major tech companies",
            "Positive market sentiment despite geopolitical concerns",
            "Technical indicators showing bullish momentum"
        ],
        "detailed_outlook": {
            "short_term": {
                "direction": "UP",
                "expected_move_pct": "2-4%",
                "confidence": "HIGH",
                "key_evidence": [
                    "Fed rate cut signals",
                    "Strong tech earnings",
                    "Positive market sentiment"
                ]
            },
            "medium_term": {
                "direction": "UP",
                "expected_move_pct": "5-8%",
                "confidence": "MEDIUM",
                "key_evidence": [
                    "Economic recovery continues",
                    "Corporate earnings growth expected"
                ]
            },
            "long_term": {
                "direction": "FLAT",
                "expected_move_pct": "0-3%",
                "confidence": "LOW",
                "key_evidence": [
                    "Geopolitical uncertainties remain",
                    "Inflation concerns persist"
                ]
            }
        },
        "dominant_theme": "Federal Reserve policy shift driving market optimism despite ongoing geopolitical tensions",
        "critical_levels": {
            "support": 440.0,
            "resistance": 465.0,
            "trigger": "Break above 465 for continued uptrend, break below 440 signals correction"
        },
        "current_price": 450.25,
        "num_articles_analyzed": 15,
        "api_used": "openai",
        "model_used": "gpt-4o-mini",
        "timestamp": datetime.now().isoformat(),
        "prediction_date": datetime.now().strftime('%m-%d-%Y'),
        "input_metadata": {
            "num_articles": 15,
            "current_price": 450.25,
            "vix_level": 18.5,
            "technical_indicators": {
                "sp500_level": 450.25,
                "sp500_change_pct": 1.2,
                "vix_level": 18.5,
                "volume_vs_avg": 1.1,
                "time": datetime.now().strftime('%Y-%m-%d %H:%M UTC')
            }
        }
    }

def create_mock_traditional_prediction() -> Dict[str, Any]:
    """Create mock traditional prediction data for testing."""
    return {
        "prediction": "positive",
        "confidence": 0.68,
        "probabilities": {
            "positive": 0.68,
            "negative": 0.22,
            "neutral": 0.10
        },
        "relevant_articles": [
            {
                "title": "Fed Signals Potential Rate Cut as Inflation Cools",
                "source": "Reuters",
                "publish_time": "2024-01-15T14:30:00Z",
                "url": "https://example.com/fed-rate-cut",
                "indicator_analysis": {
                    "label": "positive",
                    "confidence": 0.8
                }
            },
            {
                "title": "Tech Stocks Rally on Strong Earnings Reports",
                "source": "CNBC",
                "publish_time": "2024-01-15T16:45:00Z",
                "url": "https://example.com/tech-rally",
                "indicator_analysis": {
                    "label": "positive",
                    "confidence": 0.9
                }
            }
        ],
        "prediction_date": datetime.now().strftime('%m-%d-%Y'),
        "timestamp": datetime.now().isoformat()
    }

def test_data_structures():
    """Test that the data structures are properly formatted."""
    print("Testing data structures...")
    
    # Test mock articles
    articles = create_mock_articles()
    print(f"✓ Created {len(articles)} mock articles")
    
    # Test LLM prediction
    llm_prediction = create_mock_llm_prediction()
    print(f"✓ Created LLM prediction with confidence: {llm_prediction['confidence']:.1%}")
    
    # Test traditional prediction
    traditional_prediction = create_mock_traditional_prediction()
    print(f"✓ Created traditional prediction with confidence: {traditional_prediction['confidence']:.1%}")
    
    return articles, llm_prediction, traditional_prediction

def test_json_serialization():
    """Test that all data can be serialized to JSON."""
    print("\nTesting JSON serialization...")
    
    articles, llm_prediction, traditional_prediction = test_data_structures()
    
    try:
        # Test LLM prediction serialization
        llm_json = json.dumps(llm_prediction, indent=2)
        print("✓ LLM prediction serializes to JSON")
        
        # Test traditional prediction serialization
        traditional_json = json.dumps(traditional_prediction, indent=2)
        print("✓ Traditional prediction serializes to JSON")
        
        # Test articles serialization
        articles_json = json.dumps(articles, indent=2)
        print("✓ Articles serialize to JSON")
        
        return True
        
    except Exception as e:
        print(f"✗ JSON serialization failed: {e}")
        return False

def test_web_interface_compatibility():
    """Test compatibility with web interface expectations."""
    print("\nTesting web interface compatibility...")
    
    articles, llm_prediction, traditional_prediction = test_data_structures()
    
    # Test required fields for LLM prediction
    required_llm_fields = [
        'prediction', 'confidence', 'probabilities', 'key_evidence',
        'detailed_outlook', 'dominant_theme', 'critical_levels'
    ]
    
    missing_fields = []
    for field in required_llm_fields:
        if field not in llm_prediction:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"✗ Missing LLM fields: {missing_fields}")
        return False
    else:
        print("✓ All required LLM fields present")
    
    # Test probabilities structure
    probabilities = llm_prediction['probabilities']
    required_prob_fields = ['positive', 'negative', 'neutral']
    
    for field in required_prob_fields:
        if field not in probabilities:
            print(f"✗ Missing probability field: {field}")
            return False
    
    print("✓ All probability fields present")
    
    # Test detailed outlook structure
    outlook = llm_prediction['detailed_outlook']
    required_outlook_periods = ['short_term', 'medium_term', 'long_term']
    
    for period in required_outlook_periods:
        if period not in outlook:
            print(f"✗ Missing outlook period: {period}")
            return False
        
        period_data = outlook[period]
        required_period_fields = ['direction', 'expected_move_pct', 'confidence', 'key_evidence']
        
        for field in required_period_fields:
            if field not in period_data:
                print(f"✗ Missing {period} field: {field}")
                return False
    
    print("✓ All detailed outlook fields present")
    
    return True

def save_test_data():
    """Save test data to files for manual testing."""
    print("\nSaving test data to files...")
    
    articles, llm_prediction, traditional_prediction = test_data_structures()
    
    # Save LLM prediction
    with open('test_llm_prediction.json', 'w') as f:
        json.dump(llm_prediction, f, indent=2)
    print("✓ Saved test_llm_prediction.json")
    
    # Save traditional prediction
    with open('test_traditional_prediction.json', 'w') as f:
        json.dump(traditional_prediction, f, indent=2)
    print("✓ Saved test_traditional_prediction.json")
    
    # Save articles
    with open('test_articles.json', 'w') as f:
        json.dump(articles, f, indent=2)
    print("✓ Saved test_articles.json")

def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING ENHANCED WEB INTERFACE WITH LLM PREDICTIONS")
    print("=" * 60)
    
    # Run tests
    tests = [
        ("Data Structure Test", test_data_structures),
        ("JSON Serialization Test", test_json_serialization),
        ("Web Interface Compatibility Test", test_web_interface_compatibility)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = "PASS" if result else "FAIL"
        except Exception as e:
            print(f"✗ {test_name} failed with error: {e}")
            results[test_name] = "FAIL"
    
    # Save test data
    save_test_data()
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    for test_name, result in results.items():
        status_symbol = "✓" if result == "PASS" else "✗"
        print(f"{status_symbol} {test_name}: {result}")
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results.values() if r == "PASS")
    
    print(f"\nPassed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("All tests passed! 🎉")
        print("\nThe enhanced web interface is ready for LLM predictions.")
        print("Test data files have been saved for manual testing.")
    else:
        print(f"{total_tests - passed_tests} test(s) failed")

if __name__ == "__main__":
    main()
