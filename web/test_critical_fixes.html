<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Critical Fixes Test - NewsMonitor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .console-output {
            background-color: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">Critical Fixes Test</h1>
        <p class="lead">Testing the fixes for JavaScript errors and create account functionality.</p>

        <!-- Test 1: JavaScript Error Fix -->
        <div class="test-section">
            <h3><i class="bi bi-bug"></i> Test 1: JavaScript Error Fix</h3>
            <p>Testing that news.js no longer causes errors when DOM elements are missing.</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Enhanced News Interface (Should Work)</h5>
                    <div id="news-grid-container">
                        <div class="alert alert-info">Enhanced news interface placeholder</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Legacy News Interface (Should Not Cause Errors)</h5>
                    <div id="news-container" style="display: none;">
                        <div class="alert alert-warning">Legacy news interface (hidden)</div>
                    </div>
                </div>
            </div>
            
            <button class="btn btn-primary" onclick="testJavaScriptErrors()">Test JavaScript Errors</button>
            <div id="js-test-results"></div>
        </div>

        <!-- Test 2: Create Account Form -->
        <div class="test-section">
            <h3><i class="bi bi-person-plus"></i> Test 2: Create Account Form</h3>
            <p>Testing the registration form validation and submission.</p>
            
            <form id="registration-form" class="row g-3">
                <div class="col-md-6">
                    <label for="first_name" class="form-label">First Name</label>
                    <input type="text" class="form-control" name="first_name" id="first_name" placeholder="Enter first name">
                </div>
                <div class="col-md-6">
                    <label for="last_name" class="form-label">Last Name</label>
                    <input type="text" class="form-control" name="last_name" id="last_name" placeholder="Enter last name">
                </div>
                <div class="col-12">
                    <label for="username" class="form-label">Username *</label>
                    <input type="text" class="form-control" name="username" id="username" placeholder="Choose a username" required>
                </div>
                <div class="col-12">
                    <label for="email" class="form-label">Email *</label>
                    <input type="email" class="form-control" name="email" id="email" placeholder="Enter email address" required>
                </div>
                <div class="col-md-6">
                    <label for="password" class="form-label">Password *</label>
                    <input type="password" class="form-control" name="password" id="password" placeholder="Create password" required>
                </div>
                <div class="col-md-6">
                    <label for="password_confirm" class="form-label">Confirm Password *</label>
                    <input type="password" class="form-control" name="password_confirm" id="password_confirm" placeholder="Confirm password" required>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-person-plus"></i> Create Account
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" onclick="testFormValidation()">Test Validation</button>
                </div>
            </form>
            
            <div id="form-test-results" class="mt-3"></div>
        </div>

        <!-- Console Output -->
        <div class="test-section">
            <h3><i class="bi bi-terminal"></i> Console Output</h3>
            <p>JavaScript console messages will appear here:</p>
            <div id="console-output" class="console-output">
                Console initialized...<br>
            </div>
            <button class="btn btn-outline-secondary btn-sm" onclick="clearConsole()">Clear Console</button>
        </div>
    </div>

    <!-- Load Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Load our JavaScript files to test -->
    <script src="js/news-enhanced.js"></script>
    <script src="js/auth.js"></script>

    <script>
        // Console capture
        const consoleOutput = document.getElementById('console-output');
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'warn' ? '#ffd93d' : '#00ff00';
            consoleOutput.innerHTML += `<span style="color: ${color}">[${timestamp}] ${type.toUpperCase()}: ${message}</span><br>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        // Override console methods
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function clearConsole() {
            consoleOutput.innerHTML = 'Console cleared...<br>';
        }

        // Test JavaScript errors
        function testJavaScriptErrors() {
            const resultsDiv = document.getElementById('js-test-results');
            let messages = [];

            try {
                // Test that news.js elements don't cause errors
                const newsContainer = document.getElementById('news-container');
                const newsStartDate = document.getElementById('news-start-date');
                const newsEndDate = document.getElementById('news-end-date');

                if (!newsContainer) {
                    messages.push('✓ news-container not found (expected for enhanced interface)');
                }
                if (!newsStartDate) {
                    messages.push('✓ news-start-date not found (expected for enhanced interface)');
                }
                if (!newsEndDate) {
                    messages.push('✓ news-end-date not found (expected for enhanced interface)');
                }

                // Test enhanced news interface
                const enhancedContainer = document.getElementById('news-grid-container');
                if (enhancedContainer) {
                    messages.push('✓ Enhanced news interface container found');
                }

                resultsDiv.innerHTML = `<div class="success">JavaScript Error Test PASSED<br>${messages.join('<br>')}</div>`;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">JavaScript Error Test FAILED: ${error.message}</div>`;
            }
        }

        // Test form validation
        function testFormValidation() {
            const resultsDiv = document.getElementById('form-test-results');
            let messages = [];

            try {
                // Test form validation function
                if (typeof validateForm === 'function') {
                    messages.push('✓ validateForm function is available');
                    
                    // Test with empty form
                    const isValid = validateForm('registration-form');
                    messages.push(`✓ Form validation returned: ${isValid}`);
                    
                    // Test with filled form
                    document.getElementById('username').value = 'testuser';
                    document.getElementById('email').value = '<EMAIL>';
                    document.getElementById('password').value = 'TestPass123!';
                    document.getElementById('password_confirm').value = 'TestPass123!';
                    
                    const isValidFilled = validateForm('registration-form');
                    messages.push(`✓ Filled form validation returned: ${isValidFilled}`);
                    
                } else {
                    messages.push('✗ validateForm function not found');
                }

                resultsDiv.innerHTML = `<div class="success">Form Validation Test COMPLETED<br>${messages.join('<br>')}</div>`;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Form Validation Test FAILED: ${error.message}</div>`;
            }
        }

        // Test form submission
        document.getElementById('registration-form').addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent actual submission
            const resultsDiv = document.getElementById('form-test-results');
            resultsDiv.innerHTML = '<div class="info">✓ Form submission intercepted - Create Account button is working!</div>';
        });

        // Initialize tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded successfully');
            console.log('Testing critical fixes...');
            
            // Auto-run JavaScript error test
            setTimeout(() => {
                testJavaScriptErrors();
            }, 1000);
        });

        // Capture any window errors
        window.addEventListener('error', function(e) {
            console.error(`Window Error: ${e.message} at ${e.filename}:${e.lineno}`);
        });
    </script>
</body>
</html>
