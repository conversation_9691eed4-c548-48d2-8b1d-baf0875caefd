"""
Authentication forms for the NewsMonitor web application.

This module provides WTForms for user registration, login, and profile management.
"""

from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SelectField, TextAreaField
from wtforms.validators import <PERSON>Required, Email, Length, EqualTo, ValidationError
from wtforms.widgets import TextArea
from db.models import User
from db.database import get_db_manager


class LoginForm(FlaskForm):
    """User login form."""
    username = StringField('Username or Email', validators=[
        DataRequired(message='Username or email is required'),
        Length(min=3, max=80, message='Username must be between 3 and 80 characters')
    ])
    password = PasswordField('Password', validators=[
        DataRequired(message='Password is required')
    ])
    remember_me = BooleanField('Remember Me')


class RegistrationForm(FlaskForm):
    """User registration form."""
    username = <PERSON><PERSON>ield('Username', validators=[
        DataRequired(message='Username is required'),
        Length(min=3, max=80, message='Username must be between 3 and 80 characters')
    ])
    email = StringField('Email', validators=[
        DataRequired(message='Email is required'),
        Email(message='Please enter a valid email address'),
        Length(max=120, message='Email must be less than 120 characters')
    ])
    first_name = StringField('First Name', validators=[
        Length(max=50, message='First name must be less than 50 characters')
    ])
    last_name = StringField('Last Name', validators=[
        Length(max=50, message='Last name must be less than 50 characters')
    ])
    password = PasswordField('Password', validators=[
        DataRequired(message='Password is required'),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    password_confirm = PasswordField('Confirm Password', validators=[
        DataRequired(message='Please confirm your password'),
        EqualTo('password', message='Passwords must match')
    ])

    def validate_username(self, username):
        """Validate that username is unique."""
        try:
            db = get_db_manager()
            with db.connection.get_session() as session:
                user = session.query(User).filter_by(
                    username=username.data).first()
                if user:
                    raise ValidationError(
                        'Username already exists. Please choose a different one.')
        except Exception as e:
            raise ValidationError(
                'Error validating username. Please try again.')

    def validate_email(self, email):
        """Validate that email is unique."""
        try:
            db = get_db_manager()
            with db.connection.get_session() as session:
                user = session.query(User).filter_by(email=email.data).first()
                if user:
                    raise ValidationError(
                        'Email already registered. Please use a different email or login.')
        except Exception as e:
            raise ValidationError('Error validating email. Please try again.')


class ProfileForm(FlaskForm):
    """User profile management form."""
    first_name = StringField('First Name', validators=[
        Length(max=50, message='First name must be less than 50 characters')
    ])
    last_name = StringField('Last Name', validators=[
        Length(max=50, message='Last name must be less than 50 characters')
    ])
    email = StringField('Email', validators=[
        DataRequired(message='Email is required'),
        Email(message='Please enter a valid email address'),
        Length(max=120, message='Email must be less than 120 characters')
    ])

    # Email preferences
    daily_summary = BooleanField('Daily Market Summary')
    market_alerts = BooleanField('Market Alerts')
    news_digest = BooleanField('News Digest')
    email_frequency = SelectField('Email Frequency', choices=[
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('never', 'Never')
    ])

    # User preferences
    theme = SelectField('Theme', choices=[
        ('light', 'Light'),
        ('dark', 'Dark')
    ])
    timezone = SelectField('Timezone', choices=[
        ('UTC', 'UTC'),
        ('US/Eastern', 'Eastern Time'),
        ('US/Central', 'Central Time'),
        ('US/Mountain', 'Mountain Time'),
        ('US/Pacific', 'Pacific Time'),
        ('Europe/London', 'London'),
        ('Europe/Paris', 'Paris'),
        ('Asia/Tokyo', 'Tokyo'),
        ('Asia/Shanghai', 'Shanghai')
    ])


class ChangePasswordForm(FlaskForm):
    """Change password form."""
    current_password = PasswordField('Current Password', validators=[
        DataRequired(message='Current password is required')
    ])
    new_password = PasswordField('New Password', validators=[
        DataRequired(message='New password is required'),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    new_password_confirm = PasswordField('Confirm New Password', validators=[
        DataRequired(message='Please confirm your new password'),
        EqualTo('new_password', message='Passwords must match')
    ])


class ForgotPasswordForm(FlaskForm):
    """Forgot password form."""
    email = StringField('Email', validators=[
        DataRequired(message='Email is required'),
        Email(message='Please enter a valid email address')
    ])


class ResetPasswordForm(FlaskForm):
    """Reset password form."""
    password = PasswordField('New Password', validators=[
        DataRequired(message='Password is required'),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    password_confirm = PasswordField('Confirm Password', validators=[
        DataRequired(message='Please confirm your password'),
        EqualTo('password', message='Passwords must match')
    ])


class EmailPreferencesForm(FlaskForm):
    """Email preferences form."""
    daily_summary = BooleanField('Daily Market Summary',
                                 description='Receive daily summaries of market activity and news')
    market_alerts = BooleanField('Market Alerts',
                                 description='Receive alerts for significant market movements')
    news_digest = BooleanField('News Digest',
                               description='Receive curated news digests')
    email_frequency = SelectField('Email Frequency', choices=[
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('never', 'Never')
    ], description='How often you want to receive emails')

    # News source preferences
    preferred_sources = TextAreaField('Preferred News Sources',
                                      widget=TextArea(),
                                      description='Enter preferred news sources, one per line')
