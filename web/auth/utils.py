"""
Authentication utilities for the NewsMonitor web application.

This module provides utility functions for user authentication, session management,
and security features.
"""

import secrets
import string
from datetime import datetime, timedelta
from functools import wraps
from flask import current_app, url_for, request, redirect, flash, session
from flask_login import current_user
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadSignature
from db.models import User
from db.database import get_db_manager


def generate_secure_token(length=32):
    """Generate a secure random token."""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def generate_confirmation_token(email):
    """Generate email confirmation token."""
    serializer = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
    return serializer.dumps(email, salt=current_app.config.get('SECURITY_PASSWORD_SALT', 'email-confirm'))


def confirm_token(token, expiration=3600):
    """Confirm email token."""
    serializer = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
    try:
        email = serializer.loads(
            token,
            salt=current_app.config.get(
                'SECURITY_PASSWORD_SALT', 'email-confirm'),
            max_age=expiration
        )
        return email
    except (SignatureExpired, BadSignature):
        return False


def generate_reset_token(user_id):
    """Generate password reset token."""
    serializer = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
    return serializer.dumps(user_id, salt=current_app.config.get('SECURITY_PASSWORD_SALT', 'password-reset'))


def confirm_reset_token(token, expiration=3600):
    """Confirm password reset token."""
    serializer = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
    try:
        user_id = serializer.loads(
            token,
            salt=current_app.config.get(
                'SECURITY_PASSWORD_SALT', 'password-reset'),
            max_age=expiration
        )
        return user_id
    except (SignatureExpired, BadSignature):
        return False


def login_required(f):
    """Decorator to require login for a route."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function


def admin_required(f):
    """Decorator to require admin privileges for a route."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))

        # Check if user has admin privileges (you can customize this logic)
        if not getattr(current_user, 'is_admin', False):
            flash('You do not have permission to access this page.', 'error')
            return redirect(url_for('index'))

        return f(*args, **kwargs)
    return decorated_function


def verified_required(f):
    """Decorator to require email verification for a route."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))

        if not current_user.is_verified:
            flash('Please verify your email address to access this feature.', 'warning')
            return redirect(url_for('auth.unverified'))

        return f(*args, **kwargs)
    return decorated_function


def get_user_by_username_or_email(username_or_email):
    """Get user by username or email."""
    try:
        db = get_db_manager()
        user_data = db.web_service.get_user_by_username_or_email(
            username_or_email)

        if user_data:
            # Create a User-like object for Flask-Login compatibility
            class UserProxy:
                def __init__(self, data):
                    for key, value in data.items():
                        setattr(self, key, value)

                def set_password(self, password):
                    from werkzeug.security import generate_password_hash
                    self.password_hash = generate_password_hash(password)

                def check_password(self, password):
                    from werkzeug.security import check_password_hash
                    return check_password_hash(self.password_hash, password)

                def is_authenticated(self):
                    return True

                def is_active(self):
                    return self.is_active

                def is_anonymous(self):
                    return False

                def get_id(self):
                    return str(self.id)

            return UserProxy(user_data)
        return None
    except Exception as e:
        current_app.logger.error(
            f"Error getting user by username or email: {e}")
        return None


def create_user(username, email, password, first_name=None, last_name=None):
    """Create a new user."""
    try:
        db = get_db_manager()
        print("Creating user...")
        user_data = db.web_service.create_user(
            username=username,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name
        )
        print("User created:", user_data)

        if user_data:
            # Create a User-like object for Flask-Login compatibility
            class UserProxy:
                def __init__(self, data):
                    for key, value in data.items():
                        setattr(self, key, value)

                def set_password(self, password):
                    from werkzeug.security import generate_password_hash
                    self.password_hash = generate_password_hash(password)

                def check_password(self, password):
                    from werkzeug.security import check_password_hash
                    return check_password_hash(self.password_hash, password)

                def is_authenticated(self):
                    return True

                def is_active(self):
                    return self.is_active

                def is_anonymous(self):
                    return False

                def get_id(self):
                    return str(self.id)

            return UserProxy(user_data)
        return None
    except Exception as e:
        current_app.logger.error(f"Error creating user: {e}")
        return None


def update_user_login(user):
    """Update user's last login timestamp."""
    try:
        db = get_db_manager()
        return db.web_service.update_user_login(user.id)
    except Exception as e:
        current_app.logger.error(f"Error updating user login: {e}")
        return False


def update_user_profile(user, **kwargs):
    """Update user profile information."""
    try:
        db = get_db_manager()
        return db.web_service.update_user_profile(user.id, **kwargs)
    except Exception as e:
        current_app.logger.error(f"Error updating user profile: {e}")
        return False


def change_user_password(user, new_password):
    """Change user password."""
    try:
        db = get_db_manager()
        return db.web_service.change_user_password(user.id, new_password)
    except Exception as e:
        current_app.logger.error(f"Error changing user password: {e}")
        return False


def verify_user_email(user):
    """Mark user email as verified."""
    try:
        db = get_db_manager()
        return db.web_service.verify_user_email(user.id)
    except Exception as e:
        current_app.logger.error(f"Error verifying user email: {e}")
        return False


def is_safe_url(target):
    """Check if a URL is safe for redirects."""
    from urllib.parse import urlparse, urljoin
    ref_url = urlparse(request.host_url)
    test_url = urlparse(urljoin(request.host_url, target))
    return test_url.scheme in ('http', 'https') and ref_url.netloc == test_url.netloc


def get_redirect_target():
    """Get safe redirect target from request."""
    for target in request.values.get('next'), request.referrer:
        if not target:
            continue
        if is_safe_url(target):
            return target


def validate_password_strength(password):
    """Validate password strength."""
    errors = []

    if len(password) < 8:
        errors.append("Password must be at least 8 characters long")

    if not any(c.isupper() for c in password):
        errors.append("Password must contain at least one uppercase letter")

    if not any(c.islower() for c in password):
        errors.append("Password must contain at least one lowercase letter")

    if not any(c.isdigit() for c in password):
        errors.append("Password must contain at least one number")

    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    if not any(c in special_chars for c in password):
        errors.append("Password must contain at least one special character")

    return errors


def log_security_event(user_id, event_type, details=None, ip_address=None):
    """Log security events for audit purposes."""
    try:
        # This could be expanded to log to a security audit table
        current_app.logger.info(
            f"Security event - User: {user_id}, Event: {event_type}, "
            f"IP: {ip_address or request.remote_addr}, Details: {details}"
        )
    except Exception as e:
        current_app.logger.error(f"Error logging security event: {e}")


def rate_limit_check(key, limit=5, window=300):
    """Simple rate limiting check using session."""
    now = datetime.utcnow()

    # Clean up old entries
    if 'rate_limits' not in session:
        session['rate_limits'] = {}

    rate_limits = session['rate_limits']

    # Remove expired entries
    expired_keys = []
    for k, timestamps in rate_limits.items():
        rate_limits[k] = [ts for ts in timestamps if (
            now - datetime.fromisoformat(ts)).seconds < window]
        if not rate_limits[k]:
            expired_keys.append(k)

    for k in expired_keys:
        del rate_limits[k]

    # Check current key
    if key not in rate_limits:
        rate_limits[key] = []

    rate_limits[key].append(now.isoformat())
    session['rate_limits'] = rate_limits

    return len(rate_limits[key]) <= limit
