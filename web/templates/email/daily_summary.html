<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Market Summary</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }

        .email-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }

        .header p {
            color: #6c757d;
            margin: 10px 0 0 0;
            font-size: 16px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .section h2 {
            color: #212529;
            margin-top: 0;
            font-size: 20px;
            display: flex;
            align-items: center;
        }

        .section h2::before {
            content: "📊";
            margin-right: 10px;
        }

        .market-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .metric {
            background: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .metric-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #212529;
        }

        .metric-change {
            font-size: 12px;
            margin-top: 5px;
        }

        .positive {
            color: #28a745;
        }

        .negative {
            color: #dc3545;
        }

        .news-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .news-title {
            font-weight: 600;
            color: #212529;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .news-meta {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
        }

        .news-summary {
            font-size: 13px;
            color: #495057;
            line-height: 1.5;
        }

        .prediction-box {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin: 15px 0;
        }

        .prediction-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .prediction-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .prediction-confidence {
            font-size: 14px;
            opacity: 0.9;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 12px;
        }

        .footer a {
            color: #007bff;
            text-decoration: none;
        }

        .cta-button {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 15px 0;
        }

        @media (max-width: 600px) {
            body {
                padding: 10px;
            }

            .email-container {
                padding: 20px;
            }

            .market-summary {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="email-container">
        <div class="header">
            <h1>📈 Market Monitor</h1>
            <p>Daily Market Summary for {{ data.date.strftime('%B %d, %Y') }}</p>
            <p>Hello {{ user.first_name or user.username }}!</p>
        </div>

        {% if data.error %}
        <div class="section">
            <h2>⚠️ Notice</h2>
            <p>We encountered an issue generating your daily summary. Please visit the website for the latest market
                information.</p>
        </div>
        {% else %}

        <!-- Market Summary Section -->
        {% if data.market_data %}
        <div class="section">
            <h2>Market Overview</h2>
            <div class="market-summary">
                <div class="metric">
                    <div class="metric-label">Current Price</div>
                    <div class="metric-value">${{ "%.2f"|format(data.market_data.current_price or 0) }}</div>
                    {% if data.market_data.price_change %}
                    <div class="metric-change {{ 'positive' if data.market_data.price_change > 0 else 'negative' }}">
                        {{ "%.2f"|format(data.market_data.price_change) }} ({{
                        "%.2f"|format(data.market_data.price_change_percent) }}%)
                    </div>
                    {% endif %}
                </div>
                <div class="metric">
                    <div class="metric-label">Volume</div>
                    <div class="metric-value">{{ "{:,}"|format(data.market_data.volume or 0) }}</div>
                </div>
                <div class="metric">
                    <div class="metric-label">High</div>
                    <div class="metric-value">${{ "%.2f"|format(data.market_data.high or 0) }}</div>
                </div>
                <div class="metric">
                    <div class="metric-label">Low</div>
                    <div class="metric-value">${{ "%.2f"|format(data.market_data.low or 0) }}</div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Predictions Section -->
        {% if data.prediction or data.llm_prediction %}
        <div class="section">
            <h2>🔮 Market Predictions</h2>

            {% if data.prediction %}
            <div class="prediction-box">
                <div class="prediction-title">Traditional Model Prediction</div>
                <div class="prediction-value">{{ data.prediction.prediction_label or 'N/A' }}</div>
                <div class="prediction-confidence">
                    Confidence: {{ "%.1f"|format((data.prediction.confidence or 0) * 100) }}%
                </div>
            </div>
            {% endif %}

            {% if data.llm_prediction %}
            <div class="prediction-box" style="background: linear-gradient(135deg, #28a745, #20c997);">
                <div class="prediction-title">AI Analysis</div>
                <div class="prediction-value">{{ data.llm_prediction.prediction or 'N/A' }}</div>
                {% if data.llm_prediction.probabilities %}
                <div class="prediction-confidence">
                    {% for label, score in data.llm_prediction.probabilities.items() %}
                    {{ label }}: {{ "%.1f"|format(score * 100) }}%{% if not loop.last %} | {% endif %}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- Top News Section -->
        {% if data.news and data.news.all %}
        <div class="section">
            <h2>📰 Top Financial News</h2>
            {% for article in data.news.all[:5] %}
            <div class="news-item">
                <div class="news-title">{{ article.title }}</div>
                <div class="news-meta">
                    {{ article.source }} • {{ article.date.strftime('%B %d, %Y') if article.date else 'Recent' }}
                </div>
                {% if article.content %}
                <div class="news-summary">
                    {{ article.content[:150] }}{% if article.content|length > 150 %}...{% endif %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% endif %}

        <!-- Call to Action -->
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ url_for('index', _external=True) }}" class="cta-button">
                View Full Dashboard
            </a>
        </div>

        <div class="footer">
            <p>
                This email was sent to {{ user.email }} because you subscribed to daily market summaries.
                <br>
                <a href="{{ url_for('auth.profile', _external=True) }}">Update your preferences</a> |
                <a href="{{ url_for('auth.profile', _external=True) }}">Unsubscribe</a>
            </p>
            <p>
                © {{ data.date.year }} Market Monitor. All rights reserved.
            </p>
        </div>
    </div>
</body>

</html>