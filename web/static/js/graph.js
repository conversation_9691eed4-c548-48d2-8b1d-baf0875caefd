// S&P 500 Graph Functionality

document.addEventListener('DOMContentLoaded', function () {
    console.log('Graph.js loaded, checking for Plotly:', typeof Plotly);

    // DOM elements with null checks
    const graphContainer = document.getElementById('sp500-graph');
    console.log('Graph container found:', graphContainer);

    if (!graphContainer) {
        console.error('SP500 graph container not found');
        return;
    }

    const loadingIndicator = document.getElementById('loading-indicator');

    // Time period buttons
    const btn1m = document.getElementById('btn-1m');
    const btn3m = document.getElementById('btn-3m');
    const btn6m = document.getElementById('btn-6m');
    const btn1y = document.getElementById('btn-1y');
    const btnYtd = document.getElementById('btn-ytd');

    // Chart type buttons
    const btnLine = document.getElementById('btn-line');
    const btnCandle = document.getElementById('btn-candle');
    const btnArea = document.getElementById('btn-area');

    // Debug button removed

    // Current chart type
    let currentChartType = 'line';

    // Available news dates
    let availableDates = [];

    // Current graph data
    let currentData = null;

    // Flag to track if prediction has been initialized
    window.predictionInitialized = false;

    // Initialize with YTD data
    fetchAvailableDates().then(() => {
        if (btnYtd) btnYtd.classList.add('active');
        if (btnLine) btnLine.classList.add('active');
        loadYtdData();

        // Initialize prediction once when the page loads
        if (typeof window.NewsMonitor.refreshPrediction === 'function') {
            console.log('Setting up prediction initialization on first data load');
            // We'll initialize the prediction when the first data is loaded
            // This is handled in the renderGraph function
        }
    });

    // Event listeners for time period buttons with null checks
    if (btn1m) {
        btn1m.addEventListener('click', function () {
            setActiveTimeButton(this);
            load1mData();
        });
    }

    if (btn3m) {
        btn3m.addEventListener('click', function () {
            setActiveTimeButton(this);
            load3mData();
        });
    }

    if (btn6m) {
        btn6m.addEventListener('click', function () {
            setActiveTimeButton(this);
            load6mData();
        });
    }

    if (btn1y) {
        btn1y.addEventListener('click', function () {
            setActiveTimeButton(this);
            load1yData();
        });
    }

    if (btnYtd) {
        btnYtd.addEventListener('click', function () {
            setActiveTimeButton(this);
            loadYtdData();
        });
    }

    // Debug button event listener removed

    // Event listeners for chart type buttons with null checks
    if (btnLine) {
        btnLine.addEventListener('click', function () {
            setActiveChartTypeButton(this);
            currentChartType = 'line';
            if (currentData) {
                // Re-render the graph with the new chart type
                renderGraph(currentData);
            }
        });
    }

    if (btnCandle) {
        btnCandle.addEventListener('click', function () {
            setActiveChartTypeButton(this);
            currentChartType = 'candlestick';
            if (currentData) {
                // Re-render the graph with the new chart type
                renderGraph(currentData);
            }
        });
    }

    if (btnArea) {
        btnArea.addEventListener('click', function () {
            setActiveChartTypeButton(this);
            currentChartType = 'area';
            if (currentData) {
                // Re-render the graph with the new chart type
                renderGraph(currentData);
            }
        });
    }

    // Helper function to set active time period button
    function setActiveTimeButton(button) {
        [btn1m, btn3m, btn6m, btn1y, btnYtd].forEach(btn => {
            if (btn) btn.classList.remove('active');
        });
        if (button) button.classList.add('active');
    }

    // Helper function to set active chart type button
    function setActiveChartTypeButton(button) {
        [btnLine, btnCandle, btnArea].forEach(btn => {
            if (btn) btn.classList.remove('active');
        });
        if (button) button.classList.add('active');
    }

    // Fetch available news dates
    // Make this function available globally through the NewsMonitor object
    async function fetchAvailableDates(refreshGraph = false) {
        console.log('Fetching available dates, refreshGraph:', refreshGraph);
        try {
            const response = await fetch('/api/available-dates');
            const data = await response.json();

            // Store previous dates to check if we have new ones
            const previousDates = [...availableDates];

            // Update available dates
            availableDates = Object.keys(data);

            // Check if we have new dates
            const hasNewDates = availableDates.some(date => !previousDates.includes(date));

            console.log('Available dates updated:', availableDates.length, 'dates');
            console.log('Has new dates:', hasNewDates);

            // If requested to refresh the graph and we have current data, re-render it
            if (refreshGraph && currentData && (hasNewDates || refreshGraph === 'force')) {
                console.log('Refreshing graph with current data');
                renderGraph(currentData);
            }

            return availableDates;
        } catch (error) {
            console.error('Error fetching available dates:', error);
            return [];
        }
    }

    // Register the function with the global NewsMonitor object
    window.NewsMonitor.fetchAvailableDates = fetchAvailableDates;

    // Load data for different time periods
    function load1mData() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setMonth(endDate.getMonth() - 1);

        fetchSp500Data(formatDate(startDate), formatDate(endDate));
    }

    function load3mData() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setMonth(endDate.getMonth() - 3);

        fetchSp500Data(formatDate(startDate), formatDate(endDate));
    }

    function load6mData() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setMonth(endDate.getMonth() - 6);

        fetchSp500Data(formatDate(startDate), formatDate(endDate));
    }

    function load1yData() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setFullYear(endDate.getFullYear() - 1);

        fetchSp500Data(formatDate(startDate), formatDate(endDate));
    }

    function loadYtdData() {
        // Get the current date for the end date
        const endDate = new Date();

        // Use January 1 of the current year for the start date
        const startDate = new Date(endDate.getFullYear(), 0, 1); // Month is 0-indexed (0 = January)

        console.log('Loading YTD data from', formatDate(startDate), 'to', formatDate(endDate));
        fetchSp500Data(formatDate(startDate), formatDate(endDate));
    }

    // Format date as YYYY-MM-DD
    function formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    // Fetch S&P 500 data from API
    async function fetchSp500Data(startDate, endDate) {
        showLoading();
        console.log(`Fetching S&P 500 data from ${startDate} to ${endDate}`);

        try {
            const url = `/api/sp500?start_date=${startDate}&end_date=${endDate}`;
            console.log('API URL:', url);

            console.log('Sending fetch request...');
            const response = await fetch(url);
            console.log('Response received:', response);
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            console.log('Parsing JSON response...');
            const data = await response.json();
            console.log('Received data:', data);
            console.log('Data type:', typeof data);
            console.log('Data has dates:', data.dates ? data.dates.length : 'no dates');
            console.log('Data has close:', data.close ? data.close.length : 'no close');

            if (data.error) {
                throw new Error(data.error);
            }

            // Check if data is in the expected format
            if (!data.dates || !data.close) {
                console.error('Data is missing required fields:', data);
                throw new Error('Data is missing required fields');
            }

            currentData = data;
            renderGraph(data);
        } catch (error) {
            console.error('Error fetching S&P 500 data:', error);
            graphContainer.innerHTML = `<div class="alert alert-danger">Error loading S&P 500 data: ${error.message}</div>`;
        } finally {
            hideLoading();
        }
    }

    // Show loading indicator
    function showLoading() {
        if (loadingIndicator) {
            loadingIndicator.classList.remove('d-none');
        }
    }

    // Hide loading indicator
    function hideLoading() {
        if (loadingIndicator) {
            loadingIndicator.classList.add('d-none');
        }
    }

    // Render the S&P 500 graph
    async function renderGraph(data) {
        console.log('Rendering graph with data:', data);

        // Check if data is valid
        if (!data || !data.dates || !data.close || data.dates.length === 0 || data.close.length === 0) {
            console.error('Invalid data for graph:', data);
            graphContainer.innerHTML = '<div class="alert alert-danger">Error: Invalid data for graph</div>';
            return;
        }

        // Use the global formatting functions defined below

        // Update the price summary section with the latest data
        updatePriceSummary(data);

        // Initialize prediction only once when the page first loads
        if (!window.predictionInitialized && typeof window.NewsMonitor.refreshPrediction === 'function') {
            console.log('Initializing prediction with current price:', data.close[data.close.length - 1]);
            window.NewsMonitor.refreshPrediction(data.close[data.close.length - 1]);
            window.predictionInitialized = true;
        }

        // Create hover text with simplified price information
        const hoverTexts = data.dates.map((date, i) => {
            // Calculate price change from previous day
            const prevIndex = i > 0 ? i - 1 : 0;
            const priceChange = data.close[i] - data.close[prevIndex];
            const percentChange = (priceChange / data.close[prevIndex]) * 100;

            // Format the change with color
            const changeColor = priceChange >= 0 ? '#28a745' : '#dc3545';
            const changeSign = priceChange >= 0 ? '+' : '';
            const formattedChange = `<span style="color:${changeColor}">${changeSign}${formatCurrency(priceChange)} (${changeSign}${percentChange.toFixed(2)}%)</span>`;

            // Create simplified hover text
            let hoverText = `Date: <b>${date}</b><br>` +
                `Close: <b>${formatCurrency(data.close[i])}</b><br>` +
                `Open: <b>${formatCurrency(data.open[i])}</b><br>` +
                `High: <b>${formatCurrency(data.high[i])}</b><br>` +
                `Low: <b>${formatCurrency(data.low[i])}</b><br>` +
                `Return: <b>${formattedChange}</b><br>` +
                `Volume: <b>${formatVolume(data.volume[i])}</b><br>`;

            return hoverText;
        });

        // Calculate the color gradient based on price changes
        const colors = calculatePriceColors(data.close);

        // Create trace for the candlestick chart
        const candlestickTrace = {
            x: data.dates,
            open: data.open,
            high: data.high,
            low: data.low,
            close: data.close,
            type: 'candlestick',
            name: '',
            hoverinfo: 'text',
            text: hoverTexts,
            hoverlabel: {
                bgcolor: 'rgba(255, 255, 255, 0.8)',
                bordercolor: '#cccccc',
                font: { family: 'Arial', size: 12, color: '#333' }
            },
            increasing: { line: { color: '#28a745' }, fillcolor: '#28a745' },
            decreasing: { line: { color: '#dc3545' }, fillcolor: '#dc3545' },
            showlegend: false,
            visible: false
        };

        // Create trace for the line chart
        const lineTrace = {
            x: data.dates,
            y: data.close,
            type: 'scatter',
            mode: 'lines',
            name: '',
            line: {
                color: '#007bff',
                width: 2,
                shape: 'spline',
                smoothing: 0.3
            },
            hoverinfo: 'text',
            text: hoverTexts,
            hoverlabel: {
                bgcolor: 'rgba(255, 255, 255, 0.8)',
                bordercolor: '#cccccc',
                font: { family: 'Arial', size: 12, color: '#333' }
            },
            visible: true
        };

        // Create trace for the area chart (gradient fill)
        const areaTrace = {
            x: data.dates,
            y: data.close,
            type: 'scatter',
            mode: 'lines',
            name: '',
            line: {
                color: '#007bff',
                width: 2,
                shape: 'spline',
                smoothing: 0.3
            },
            fill: 'tozeroy',
            fillcolor: 'rgba(0, 123, 255, 0.1)',
            hoverinfo: 'text',
            text: hoverTexts,
            hoverlabel: {
                bgcolor: 'rgba(255, 255, 255, 0.8)',
                bordercolor: '#cccccc',
                font: { family: 'Arial', size: 12, color: '#333' }
            },
            visible: false
        };

        // Create trace for the volume chart
        const volumeTrace = {
            x: data.dates,
            y: data.volume,
            type: 'bar',
            name: '',
            marker: {
                color: colors,
                opacity: 0.4
            },
            yaxis: 'y2',
            hoverinfo: 'skip', // Skip separate hover for volume to avoid duplication
            visible: true
        };

        // Layout configuration
        const layout = {
            // Make the graph responsive to container size
            autosize: true,
            // Use a fixed height instead of trying to calculate from container
            height: 350,
            // Set a fixed width to ensure proper rendering
            width: graphContainer.clientWidth,
            // Remove title as we now have the price summary section
            xaxis: {
                title: '',
                showgrid: false,
                zeroline: false,
                automargin: true,
                rangeslider: {
                    visible: false
                }
            },
            // Set up the layout for price and volume (overlaid)
            yaxis: {
                title: '',
                showgrid: true,
                zeroline: false,
                side: 'left',
                tickformat: '$,.2f',
                automargin: true
            },
            yaxis2: {
                title: '',
                showgrid: false,
                zeroline: false,
                side: 'left',
                showticklabels: false,
                overlaying: 'y',
                // Scale the volume to be visible but not overwhelming
                range: [0, Math.max(...data.volume) * 8]
            },
            margin: { t: 5, b: 20, l: 50, r: 10 },
            // hovermode: 'x unified',
            hoverdistance: 50,
            showlegend: false,
            plot_bgcolor: '#ffffff',
            paper_bgcolor: '#ffffff',
            hoverlabel: {
                namelength: -1,
                align: 'left',
                bgcolor: 'rgba(255, 255, 255, 0.8)',
                bordercolor: '#cccccc',
                font: { family: 'Arial', size: 12, color: '#333' }
            }
        };

        // Plot options
        const config = {
            responsive: true,
            // This ensures the plot resizes with its container
            fillFrame: true,
            // This ensures the plot maintains its aspect ratio
            frameMargins: 0,
            displayModeBar: true,
            displaylogo: false,
            modeBarButtonsToRemove: [
                'lasso2d',
                'select2d',
                'autoScale2d',
                'hoverClosestCartesian',
                'hoverCompareCartesian',
                'toggleSpikelines'
            ],
            toImageButtonOptions: {
                format: 'png',
                filename: 'spy_chart',
                height: 500,
                width: 800,
                scale: 2
            }
        };

        // Add a resize event listener to make sure the graph always fits the container
        const resizeGraph = function () {
            const containerWidth = graphContainer.clientWidth;
            // Use a fixed height of 350px instead of trying to fill the container
            const fixedHeight = 350;
            if (containerWidth > 0) {
                Plotly.relayout(graphContainer, {
                    height: fixedHeight,
                    width: containerWidth,
                    autosize: true
                });
            }
        };

        // Debounce function to limit how often the resize event fires
        function debounce(func, wait) {
            let timeout;
            return function () {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), wait);
            };
        }

        // Add the resize event with debouncing
        const debouncedResize = debounce(resizeGraph, 250);
        window.addEventListener('resize', debouncedResize);

        // Also trigger resize when the DOM is fully loaded
        document.addEventListener('DOMContentLoaded', resizeGraph);

        // And trigger once after a short delay to ensure proper sizing
        setTimeout(resizeGraph, 500);

        // Add a mutation observer to detect changes in the container size
        const resizeObserver = new ResizeObserver(debouncedResize);
        resizeObserver.observe(graphContainer);

        // Create the plot with the selected chart type and volume trace
        let traces = [];

        // Add the appropriate chart trace based on the current chart type
        if (currentChartType === 'line') {
            lineTrace.visible = true;
            traces.push(lineTrace);
        } else if (currentChartType === 'candlestick') {
            candlestickTrace.visible = true;
            traces.push(candlestickTrace);
        } else if (currentChartType === 'area') {
            areaTrace.visible = true;
            traces.push(areaTrace);
        }

        // Always add the volume trace
        traces.push(volumeTrace);

        // Create the plot
        Plotly.newPlot(graphContainer, traces, layout, config);

        // Add event listener for hover to update the price summary
        graphContainer.on('plotly_hover', function (data) {
            if (data.points && data.points.length > 0) {
                const point = data.points[0];
                const index = point.pointIndex;

                // Only update if we have a valid index and it's a price point
                if (index >= 0 && point.data.y && (point.data.type === 'scatter' || point.data.type === 'candlestick')) {
                    updateHoverInfo(data, index);
                }
            }
        });

        // Add event listener for unhover to reset the price summary
        graphContainer.on('plotly_unhover', function () {
            // Reset to the latest data
            updatePriceSummary(data);
        });
    }

    // Function to update the price summary section with the latest data (Yahoo Finance Style)
    function updatePriceSummary(data) {
        if (!data || !data.dates || !data.close || data.dates.length === 0) {
            return;
        }

        // Get the latest data
        const lastIndex = data.dates.length - 1;
        const currentPrice = data.close[lastIndex];
        const previousPrice = lastIndex > 0 ? data.close[lastIndex - 1] : data.open[lastIndex];
        const priceChange = currentPrice - previousPrice;
        const percentChange = (priceChange / previousPrice) * 100;

        // Calculate 52-week range
        let yearLow = Number.MAX_VALUE;
        let yearHigh = Number.MIN_VALUE;
        let totalVolume = 0;
        let volumeCount = 0;

        // Use the last year of data or all available data if less than a year
        const startIndex = Math.max(0, lastIndex - 252); // Approximately 252 trading days in a year
        for (let i = startIndex; i <= lastIndex; i++) {
            yearLow = Math.min(yearLow, data.low[i]);
            yearHigh = Math.max(yearHigh, data.high[i]);

            // Calculate average volume (last 30 days)
            if (i >= lastIndex - 30) {
                totalVolume += data.volume[i];
                volumeCount++;
            }
        }

        // Calculate average volume
        const avgVolume = volumeCount > 0 ? totalVolume / volumeCount : 0;

        // Format the values
        const formattedPrice = formatCurrency(currentPrice);
        const formattedChange = formatCurrency(priceChange);
        const formattedPercent = formatPercentage(percentChange);

        // Update the DOM elements with null checks
        const currentPriceElement = document.getElementById('current-price');
        if (currentPriceElement) {
            currentPriceElement.textContent = formattedPrice;
        }

        const priceChangeElement = document.getElementById('price-change');
        if (priceChangeElement) {
            priceChangeElement.textContent = `${formattedChange} (${formattedPercent})`;

            // Add color class based on price change
            priceChangeElement.className = 'fs-5 fw-bold';
            if (priceChange > 0) {
                priceChangeElement.classList.add('price-up');
            } else if (priceChange < 0) {
                priceChangeElement.classList.add('price-down');
            }
        }

        // Update last updated time - use current date and time instead of the last date in the data
        // This ensures we show the current date even if the price data is from a previous trading day
        const currentDate = new Date();
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            lastUpdatedElement.textContent = `As of ${currentDate.toLocaleDateString()} ${currentDate.toLocaleTimeString()}`;
        }

        // Update other price information with null checks
        const prevCloseElement = document.getElementById('prev-close');
        if (prevCloseElement) {
            prevCloseElement.textContent = formatCurrency(previousPrice);
        }

        const openPriceElement = document.getElementById('open-price');
        if (openPriceElement) {
            openPriceElement.textContent = formatCurrency(data.open[lastIndex]);
        }

        const dayRangeElement = document.getElementById('day-range');
        if (dayRangeElement) {
            dayRangeElement.textContent = `${formatCurrency(data.low[lastIndex])} - ${formatCurrency(data.high[lastIndex])}`;
        }

        const yearRangeElement = document.getElementById('year-range');
        if (yearRangeElement) {
            yearRangeElement.textContent = `${formatCurrency(yearLow)} - ${formatCurrency(yearHigh)}`;
        }

        const volumeElement = document.getElementById('volume');
        if (volumeElement) {
            volumeElement.textContent = formatVolume(data.volume[lastIndex]);
        }

        const avgVolumeElement = document.getElementById('avg-volume');
        if (avgVolumeElement) {
            avgVolumeElement.textContent = formatVolume(avgVolume);
        }
    }

    // Function to update hover information (Yahoo Finance Style)
    function updateHoverInfo(hoverData, index) {
        const point = hoverData.points[0];
        const data = point.data;

        // For candlestick, we need to access the data differently
        let currentPrice, openPrice, highPrice, lowPrice, volume;
        let date;

        if (data.type === 'candlestick') {
            currentPrice = data.close[index];
            openPrice = data.open[index];
            highPrice = data.high[index];
            lowPrice = data.low[index];
            date = data.x[index];

            // Find the volume data
            const volumeTrace = hoverData.points.find(p => p.data.name === 'Volume');
            volume = volumeTrace ? volumeTrace.y : 0;
        } else {
            // For line or area chart
            currentPrice = data.y[index];
            date = data.x[index];

            // Find the corresponding data in the original dataset
            const dataIndex = currentData.dates.indexOf(date);

            if (dataIndex >= 0) {
                openPrice = currentData.open[dataIndex];
                highPrice = currentData.high[dataIndex];
                lowPrice = currentData.low[dataIndex];
                volume = currentData.volume[dataIndex];
            }
        }

        // Calculate price change from previous point
        const previousIndex = index > 0 ? index - 1 : 0;
        const previousPrice = data.type === 'candlestick' ? data.close[previousIndex] : data.y[previousIndex];
        const priceChange = currentPrice - previousPrice;
        const percentChange = (priceChange / previousPrice) * 100;

        // Calculate 52-week range and average volume
        // We'll use the same values as in updatePriceSummary for consistency
        // In a real application, you might want to calculate these based on the hovered date
        let yearLow = Number.MAX_VALUE;
        let yearHigh = Number.MIN_VALUE;
        let totalVolume = 0;
        let volumeCount = 0;

        // Use the last year of data or all available data if less than a year
        const lastIndex = currentData.dates.length - 1;
        const startIndex = Math.max(0, lastIndex - 252); // Approximately 252 trading days in a year
        for (let i = startIndex; i <= lastIndex; i++) {
            yearLow = Math.min(yearLow, currentData.low[i]);
            yearHigh = Math.max(yearHigh, currentData.high[i]);

            // Calculate average volume (last 30 days)
            if (i >= lastIndex - 30) {
                totalVolume += currentData.volume[i];
                volumeCount++;
            }
        }

        // Calculate average volume
        const avgVolume = volumeCount > 0 ? totalVolume / volumeCount : 0;

        // Format the values
        const formattedPrice = formatCurrency(currentPrice);
        const formattedChange = formatCurrency(priceChange);
        const formattedPercent = formatPercentage(percentChange);

        // Update the DOM elements with null checks
        const currentPriceElement = document.getElementById('current-price');
        if (currentPriceElement) {
            currentPriceElement.textContent = formattedPrice;
        }

        const priceChangeElement = document.getElementById('price-change');
        if (priceChangeElement) {
            priceChangeElement.textContent = `${formattedChange} (${formattedPercent})`;

            // Add color class based on price change
            priceChangeElement.className = 'fs-5 fw-bold';
            if (priceChange > 0) {
                priceChangeElement.classList.add('price-up');
            } else if (priceChange < 0) {
                priceChangeElement.classList.add('price-down');
            }
        }

        // Format the date for display
        const hoverDate = new Date(date);
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            lastUpdatedElement.textContent = `Data for ${hoverDate.toLocaleDateString()}`;
        }

        // Update other price information with null checks
        const prevCloseElement = document.getElementById('prev-close');
        if (prevCloseElement) {
            prevCloseElement.textContent = formatCurrency(previousPrice);
        }

        const openPriceElement = document.getElementById('open-price');
        if (openPriceElement) {
            openPriceElement.textContent = formatCurrency(openPrice);
        }

        const dayRangeElement = document.getElementById('day-range');
        if (dayRangeElement) {
            dayRangeElement.textContent = `${formatCurrency(lowPrice)} - ${formatCurrency(highPrice)}`;
        }

        const yearRangeElement = document.getElementById('year-range');
        if (yearRangeElement) {
            yearRangeElement.textContent = `${formatCurrency(yearLow)} - ${formatCurrency(yearHigh)}`;
        }

        const volumeElement = document.getElementById('volume');
        if (volumeElement) {
            volumeElement.textContent = formatVolume(volume);
        }

        const avgVolumeElement = document.getElementById('avg-volume');
        if (avgVolumeElement) {
            avgVolumeElement.textContent = formatVolume(avgVolume);
        }
    }

    // Function to calculate colors for volume bars based on price changes
    function calculatePriceColors(prices) {
        return prices.map((price, i) => {
            if (i === 0) return '#007bff';
            return price >= prices[i - 1] ? '#28a745' : '#dc3545';
        });
    }

    // Format currency for display
    function formatCurrency(value) {
        if (value === undefined || value === null) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(value);
    }

    // Format volume for display
    function formatVolume(value) {
        if (value === undefined || value === null) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            notation: 'compact',
            compactDisplay: 'short'
        }).format(value);
    }

    // Format percentage for display
    function formatPercentage(value) {
        if (value === undefined || value === null) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'percent',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
            signDisplay: 'always'
        }).format(value / 100);
    }
});
