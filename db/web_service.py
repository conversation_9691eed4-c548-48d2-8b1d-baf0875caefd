"""
Web service database operations.

This module provides database operations specifically for the web application,
ensuring all methods return Python dictionaries instead of SQLAlchemy model instances
to prevent session binding issues.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload

from db.database_connection import DatabaseConnection
from db.models import User
from utils.logging_config import get_web_logger

logger = get_web_logger(__name__)


class WebService:
    """Service class for web-specific database operations."""

    def __init__(self, connection: DatabaseConnection):
        self.connection = connection

    def get_user_by_username_or_email(self, username_or_email: str) -> Optional[Dict[str, Any]]:
        """
        Get user by username or email.
        
        Args:
            username_or_email: Username or email to search for
            
        Returns:
            User dictionary or None if not found
        """
        try:
            with self.connection.get_session() as session:
                # Try to find by username first
                stmt = select(User).where(User.username == username_or_email)
                user = session.execute(stmt).scalar_one_or_none()
                
                if user:
                    return self._user_to_dict(user)

                # If not found, try by email
                stmt = select(User).where(User.email == username_or_email)
                user = session.execute(stmt).scalar_one_or_none()
                
                if user:
                    return self._user_to_dict(user)
                    
                return None
                
        except Exception as e:
            logger.error(f"Error getting user by username or email: {e}")
            return None

    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get user by ID.
        
        Args:
            user_id: User ID to search for
            
        Returns:
            User dictionary or None if not found
        """
        try:
            with self.connection.get_session() as session:
                stmt = select(User).where(User.id == user_id)
                user = session.execute(stmt).scalar_one_or_none()
                
                if user:
                    return self._user_to_dict(user)
                    
                return None
                
        except Exception as e:
            logger.error(f"Error getting user by ID: {e}")
            return None

    def create_user(self, username: str, email: str, password: str, 
                   first_name: Optional[str] = None, last_name: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Create a new user.
        
        Args:
            username: Username for the new user
            email: Email for the new user
            password: Password for the new user
            first_name: Optional first name
            last_name: Optional last name
            
        Returns:
            User dictionary or None if creation failed
        """
        try:
            with self.connection.get_session() as session:
                # Create new user
                user = User(
                    username=username,
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                user.set_password(password)

                session.add(user)
                session.commit()
                session.refresh(user)

                return self._user_to_dict(user)
                
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return None

    def update_user_login(self, user_id: int) -> bool:
        """
        Update user's last login timestamp.
        
        Args:
            user_id: ID of the user to update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.connection.get_session() as session:
                stmt = select(User).where(User.id == user_id)
                user = session.execute(stmt).scalar_one_or_none()
                
                if user:
                    user.last_login = datetime.utcnow()
                    user.updated_at = datetime.utcnow()
                    session.commit()
                    return True
                    
                return False
                
        except Exception as e:
            logger.error(f"Error updating user login: {e}")
            return False

    def update_user_profile(self, user_id: int, **kwargs) -> bool:
        """
        Update user profile information.
        
        Args:
            user_id: ID of the user to update
            **kwargs: Fields to update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.connection.get_session() as session:
                stmt = select(User).where(User.id == user_id)
                user = session.execute(stmt).scalar_one_or_none()
                
                if user:
                    # Update allowed fields
                    allowed_fields = ['first_name', 'last_name', 'email', 
                                    'email_preferences', 'user_preferences']

                    for field, value in kwargs.items():
                        if field in allowed_fields and hasattr(user, field):
                            setattr(user, field, value)

                    user.updated_at = datetime.utcnow()
                    session.commit()
                    return True
                    
                return False
                
        except Exception as e:
            logger.error(f"Error updating user profile: {e}")
            return False

    def change_user_password(self, user_id: int, new_password: str) -> bool:
        """
        Change user password.
        
        Args:
            user_id: ID of the user
            new_password: New password
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.connection.get_session() as session:
                stmt = select(User).where(User.id == user_id)
                user = session.execute(stmt).scalar_one_or_none()
                
                if user:
                    user.set_password(new_password)
                    user.updated_at = datetime.utcnow()
                    session.commit()
                    return True
                    
                return False
                
        except Exception as e:
            logger.error(f"Error changing user password: {e}")
            return False

    def verify_user_email(self, user_id: int) -> bool:
        """
        Mark user email as verified.
        
        Args:
            user_id: ID of the user
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.connection.get_session() as session:
                stmt = select(User).where(User.id == user_id)
                user = session.execute(stmt).scalar_one_or_none()
                
                if user:
                    user.is_verified = True
                    user.updated_at = datetime.utcnow()
                    session.commit()
                    return True
                    
                return False
                
        except Exception as e:
            logger.error(f"Error verifying user email: {e}")
            return False

    def _user_to_dict(self, user: User) -> Dict[str, Any]:
        """
        Convert User model to dictionary to prevent session binding issues.
        
        Args:
            user: User model instance
            
        Returns:
            User data as dictionary
        """
        return {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_active': user.is_active,
            'is_verified': user.is_verified,
            'created_at': user.created_at,
            'updated_at': user.updated_at,
            'last_login': user.last_login,
            'email_preferences': user.email_preferences,
            'user_preferences': user.user_preferences,
            'password_hash': user.password_hash  # Include for authentication
        }
