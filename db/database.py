"""
Database module for managing articles and chunks with vector similarity search.

This module provides a comprehensive database interface for storing and querying
articles with their associated text chunks, including vector embeddings for
semantic search capabilities.
"""

import os

from dotenv import load_dotenv
from sqlalchemy import text

from db.database_connection import DatabaseConnection
from db.article_service import ArticleService
from db.market_data_service import MarketDataService
from db.llm_api_service import LlmApiService
from db.web_service import WebService
from db.email_service import EmailService
from db.models import Base

from utils.logging_config import configure_logging

# Configure logging
logger = configure_logging(__name__, log_file='database.log')

# Load environment variables
load_dotenv()

# Configuration
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    raise ValueError("DATABASE_URL environment variable is required")


class DatabaseInitializer:
    """Handles database schema and index creation."""

    def __init__(self, connection: DatabaseConnection):
        self.connection = connection

    def initialize(self):
        """Initialize database schema and indexes."""
        self._create_extensions()
        self._create_schema()
        self._create_indexes()

    def _create_extensions(self):
        """Create required PostgreSQL extensions."""
        with self.connection.engine.begin() as conn:
            logger.info("Installing pgvector extension...")
            conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))

    def _create_schema(self):
        """Create database tables."""
        logger.info("Creating database schema...")
        Base.metadata.create_all(bind=self.connection.engine)

    def _create_indexes(self):
        """Create database indexes for optimal performance."""
        indexes = [
            # Vector similarity index
            """
            CREATE INDEX IF NOT EXISTS ix_chunks_embedding_cosine 
            ON chunks USING hnsw (embedding vector_cosine_ops)
            WITH (m = 16, ef_construction = 200)
            """,

            # Date range queries
            """
            CREATE INDEX IF NOT EXISTS ix_chunks_article_date
            ON chunks (article_date)
            """,

            # Title searches
            """
            CREATE INDEX IF NOT EXISTS ix_chunks_article_title
            ON chunks (article_title)
            """,

            # Recent chunks optimization
            """
            CREATE INDEX IF NOT EXISTS ix_chunks_recent_date
            ON chunks (article_date) 
            WHERE article_date >= '2024-01-01'
            """,

            # Composite index for common queries
            """
            CREATE INDEX IF NOT EXISTS ix_chunks_date_title 
            ON chunks (article_date, article_title)
            """,

            # Article indexes
            """
            CREATE INDEX IF NOT EXISTS ix_articles_date_source
            ON articles (date, source)
            """,

            """
            CREATE INDEX IF NOT EXISTS ix_articles_url_unique
            ON articles (url)
            """
        ]

        with self.connection.engine.connect() as conn:
            logger.info("Creating database indexes...")
            for index_sql in indexes:
                try:
                    conn.execute(text(index_sql))
                except Exception as e:
                    logger.warning("Failed to create index: %s", str(e))

            # Remove deprecated indexes
            conn.execute(text("DROP INDEX IF EXISTS ix_chunks_embedding_l2"))
            conn.commit()


class DatabaseManager:
    """
    Main database manager class for article and chunk operations.

    Provides high-level interface for storing, querying, and managing
    articles and their associated text chunks with vector embeddings.
    """

    def __init__(self, database_url: str = DATABASE_URL):
        self.connection = DatabaseConnection(database_url)

        # Initialize database
        initializer = DatabaseInitializer(self.connection)
        initializer.initialize()

        self.article_service = ArticleService(self.connection)
        self.market_data_service = MarketDataService(self.connection)
        self.llm_api_service = LlmApiService(self.connection)
        self.web_service = WebService(self.connection)
        self.email_service = EmailService(self.connection)

        logger.info("DatabaseManager initialized successfully")

    def close(self):
        """Close database connections and cleanup resources."""
        logger.info("Closing DatabaseManager...")
        self.connection.close()


_db_manager = None


def get_db_manager():
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager
