"""
Email service database operations.

This module provides database operations specifically for email functionality,
ensuring all methods return Python dictionaries instead of SQLAlchemy model instances
to prevent session binding issues.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
from sqlalchemy import select, and_, func
from sqlalchemy.orm import selectinload

from db.database_connection import DatabaseConnection
from db.models import User, EmailLog
from utils.logging_config import get_web_logger

logger = get_web_logger(__name__)


class EmailService:
    """Service class for email-specific database operations."""

    def __init__(self, connection: DatabaseConnection):
        self.connection = connection

    def get_all_users_for_email(self) -> List[Dict[str, Any]]:
        """
        Get all active users for email sending.
        
        Returns:
            List of user dictionaries
        """
        try:
            with self.connection.get_session() as session:
                stmt = select(User).where(
                    and_(
                        User.is_active == True,
                        User.is_verified == True
                    )
                )
                users = session.execute(stmt).scalars().all()
                
                return [self._user_to_dict(user) for user in users]
                
        except Exception as e:
            logger.error(f"Error getting users for email: {e}")
            return []

    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get user by ID for email operations.
        
        Args:
            user_id: User ID to search for
            
        Returns:
            User dictionary or None if not found
        """
        try:
            with self.connection.get_session() as session:
                stmt = select(User).where(User.id == user_id)
                user = session.execute(stmt).scalar_one_or_none()
                
                if user:
                    return self._user_to_dict(user)
                    
                return None
                
        except Exception as e:
            logger.error(f"Error getting user by ID: {e}")
            return None

    def log_email(self, user_id: Optional[int], email_type: str, subject: str, 
                  recipient: str, status: str, error_message: Optional[str] = None) -> bool:
        """
        Log email sending attempt.
        
        Args:
            user_id: ID of the user (optional)
            email_type: Type of email
            subject: Email subject
            recipient: Email recipient
            status: Email status (sent, failed)
            error_message: Optional error message
            
        Returns:
            True if logged successfully, False otherwise
        """
        try:
            with self.connection.get_session() as session:
                email_log = EmailLog(
                    user_id=user_id,
                    email_type=email_type,
                    subject=subject,
                    recipient=recipient,
                    status=status,
                    error_message=error_message,
                    sent_at=datetime.utcnow()
                )
                
                session.add(email_log)
                session.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error logging email: {e}")
            return False

    def check_email_sent_today(self, user_id: int, email_type: str) -> bool:
        """
        Check if an email of the given type was already sent to the user today.
        
        Args:
            user_id: ID of the user
            email_type: Type of email to check
            
        Returns:
            True if email was already sent today, False otherwise
        """
        try:
            with self.connection.get_session() as session:
                today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
                
                stmt = select(EmailLog).where(
                    and_(
                        EmailLog.user_id == user_id,
                        EmailLog.email_type == email_type,
                        EmailLog.status == 'sent',
                        EmailLog.sent_at >= today_start
                    )
                )
                
                result = session.execute(stmt).scalar_one_or_none()
                return result is not None
                
        except Exception as e:
            logger.error(f"Error checking if email sent today: {e}")
            return False

    def get_email_logs(self, user_id: Optional[int] = None, 
                      email_type: Optional[str] = None,
                      limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get email logs with optional filtering.
        
        Args:
            user_id: Optional user ID filter
            email_type: Optional email type filter
            limit: Optional limit on number of results
            
        Returns:
            List of email log dictionaries
        """
        try:
            with self.connection.get_session() as session:
                stmt = select(EmailLog)
                
                conditions = []
                if user_id:
                    conditions.append(EmailLog.user_id == user_id)
                if email_type:
                    conditions.append(EmailLog.email_type == email_type)
                
                if conditions:
                    stmt = stmt.where(and_(*conditions))
                
                stmt = stmt.order_by(EmailLog.sent_at.desc())
                
                if limit:
                    stmt = stmt.limit(limit)
                
                logs = session.execute(stmt).scalars().all()
                
                return [self._email_log_to_dict(log) for log in logs]
                
        except Exception as e:
            logger.error(f"Error getting email logs: {e}")
            return []

    def get_email_stats(self, days: int = 30) -> Dict[str, Any]:
        """
        Get email statistics for the last N days.
        
        Args:
            days: Number of days to look back
            
        Returns:
            Dictionary with email statistics
        """
        try:
            with self.connection.get_session() as session:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Total emails sent
                total_stmt = select(func.count(EmailLog.id)).where(
                    and_(
                        EmailLog.sent_at >= cutoff_date,
                        EmailLog.status == 'sent'
                    )
                )
                total_sent = session.execute(total_stmt).scalar() or 0
                
                # Total emails failed
                failed_stmt = select(func.count(EmailLog.id)).where(
                    and_(
                        EmailLog.sent_at >= cutoff_date,
                        EmailLog.status == 'failed'
                    )
                )
                total_failed = session.execute(failed_stmt).scalar() or 0
                
                # Emails by type
                type_stmt = select(
                    EmailLog.email_type,
                    func.count(EmailLog.id).label('count')
                ).where(
                    and_(
                        EmailLog.sent_at >= cutoff_date,
                        EmailLog.status == 'sent'
                    )
                ).group_by(EmailLog.email_type)
                
                type_results = session.execute(type_stmt).all()
                by_type = {row.email_type: row.count for row in type_results}
                
                return {
                    'total_sent': total_sent,
                    'total_failed': total_failed,
                    'success_rate': (total_sent / (total_sent + total_failed) * 100) if (total_sent + total_failed) > 0 else 0,
                    'by_type': by_type,
                    'period_days': days
                }
                
        except Exception as e:
            logger.error(f"Error getting email stats: {e}")
            return {
                'total_sent': 0,
                'total_failed': 0,
                'success_rate': 0,
                'by_type': {},
                'period_days': days
            }

    def _user_to_dict(self, user: User) -> Dict[str, Any]:
        """
        Convert User model to dictionary to prevent session binding issues.
        
        Args:
            user: User model instance
            
        Returns:
            User data as dictionary
        """
        return {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_active': user.is_active,
            'is_verified': user.is_verified,
            'created_at': user.created_at,
            'updated_at': user.updated_at,
            'last_login': user.last_login,
            'email_preferences': user.email_preferences,
            'user_preferences': user.user_preferences
        }

    def _email_log_to_dict(self, email_log: EmailLog) -> Dict[str, Any]:
        """
        Convert EmailLog model to dictionary to prevent session binding issues.
        
        Args:
            email_log: EmailLog model instance
            
        Returns:
            Email log data as dictionary
        """
        return {
            'id': email_log.id,
            'user_id': email_log.user_id,
            'email_type': email_log.email_type,
            'subject': email_log.subject,
            'recipient': email_log.recipient,
            'status': email_log.status,
            'error_message': email_log.error_message,
            'sent_at': email_log.sent_at
        }
